"use client"

import { useSession } from "next-auth/react"
import Link from "next/link"
import { Navbar } from "@/components/layout/navbar"
import { Button } from "@/components/ui/button"
import { LoadingSpinner } from "@/components/ui/loading-spinner"
import { BillsFilters } from "@/components/bills/bills-filters"
import { BillsStats } from "@/components/bills/bills-stats"
import { BillsList } from "@/components/bills/bills-list"
import { useBills } from "@/hooks/use-api"
import { useBillsFilters } from "@/hooks/use-bills-filters"
import { Plus } from "lucide-react"

export default function BillsPage() {
  const { data: session } = useSession()
  const { toast } = useToast()
  const [bills, setBills] = useState<Bill[]>([])
  const [filteredBills, setFilteredBills] = useState<Bill[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const [sortField, setSortField] = useState<SortField>("createdAt")
  const [sortOrder, setSortOrder] = useState<SortOrder>("desc")
  const [filterStatus, setFilterStatus] = useState<FilterStatus>("all")

  useEffect(() => {
    const fetchBills = async () => {
      try {
        const response = await fetch("/api/bills")
        if (response.ok) {
          const data = await response.json()
          setBills(data.bills || [])
        } else {
          toast({
            title: "Ошибка",
            description: "Не удалось загрузить счета",
            variant: "destructive"
          })
        }
      } catch (error) {
        console.error("Error fetching bills:", error)
        toast({
          title: "Ошибка",
          description: "Не удалось загрузить счета",
          variant: "destructive"
        })
      } finally {
        setIsLoading(false)
      }
    }

    if (session?.user?.id) {
      fetchBills()
    }
  }, [session, toast])

  // Фильтрация и сортировка счетов
  useEffect(() => {
    const filtered = bills.filter(bill => {
      // Поиск по названию
      const matchesSearch = bill.title.toLowerCase().includes(searchQuery.toLowerCase())

      // Фильтрация по статусу
      let matchesStatus = true
      if (filterStatus !== "all") {
        const userParticipant = bill.participants.find(p => p.user._id === session?.user?.id)
        const isCreator = bill.creator._id === session?.user?.id

        if (filterStatus === "paid") {
          matchesStatus = userParticipant?.status === "paid" || isCreator
        } else if (filterStatus === "unpaid") {
          matchesStatus = userParticipant?.status === "unpaid" && !isCreator
        } else if (filterStatus === "pending") {
          matchesStatus = bill.participants.some(p => p.status === "unpaid")
        }
      }

      return matchesSearch && matchesStatus
    })

    // Сортировка
    filtered.sort((a, b) => {
      let aValue: number | string, bValue: number | string

      switch (sortField) {
        case "createdAt":
          aValue = new Date(a.createdAt).getTime()
          bValue = new Date(b.createdAt).getTime()
          break
        case "total":
          aValue = a.total
          bValue = b.total
          break
        case "title":
          aValue = a.title.toLowerCase()
          bValue = b.title.toLowerCase()
          break
        default:
          aValue = new Date(a.createdAt).getTime()
          bValue = new Date(b.createdAt).getTime()
      }

      if (sortOrder === "asc") {
        return aValue > bValue ? 1 : -1
      } else {
        return aValue < bValue ? 1 : -1
      }
    })

    setFilteredBills(filtered)
  }, [bills, searchQuery, sortField, sortOrder, filterStatus, session])

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc")
    } else {
      setSortField(field)
      setSortOrder("desc")
    }
  }

  const getBillStatus = (bill: Bill) => {
    const userParticipant = bill.participants.find(p => p.user._id === session?.user?.id)
    const isCreator = bill.creator._id === session?.user?.id
    const allPaid = bill.participants.every(p => p.status === "paid")

    if (allPaid) return "completed"
    if (isCreator) return "creator"
    if (userParticipant?.status === "paid") return "paid"
    return "unpaid"
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "completed":
        return <Badge className="bg-green-100 text-green-800">Завершен</Badge>
      case "creator":
        return <Badge className="bg-blue-100 text-blue-800">Создатель</Badge>
      case "paid":
        return <Badge className="bg-green-100 text-green-800">Оплачено</Badge>
      case "unpaid":
        return <Badge className="bg-red-100 text-red-800">Не оплачено</Badge>
      default:
        return <Badge className="bg-gray-100 text-gray-800">Неизвестно</Badge>
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-100 via-blue-50 to-indigo-100">
        <Navbar />
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center h-96">
            <div className="text-center">
              <div className="relative">
                <div className="animate-spin rounded-full h-16 w-16 border-4 border-blue-200 border-t-blue-600 mx-auto"></div>
                <div className="absolute inset-0 rounded-full h-16 w-16 border-4 border-transparent border-r-purple-400 animate-spin mx-auto" style={{ animationDirection: 'reverse', animationDuration: '1.5s' }}></div>
              </div>
              <p className="mt-6 text-lg text-gray-600 font-medium">Загрузка счетов...</p>
              <p className="mt-2 text-sm text-gray-500">Получаем ваши данные</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-100 via-blue-50 to-indigo-100">
      <Navbar />

      <div className="container mx-auto px-4 py-8">
          {/* Header */}
          <div className="flex flex-col lg:flex-row lg:items-center justify-between mb-10 gap-4">
            <div>
              <h1 className="text-4xl font-bold text-gray-900 mb-2">Все счета</h1>
              <p className="text-lg text-gray-600">
                Управляйте всеми своими счетами в одном месте
              </p>
            </div>
            <Link href="/bills/new">
              <Button className="bg-blue-600 hover:bg-blue-700 border-0 shadow-lg px-8 py-3 text-base">
                <Plus className="h-5 w-5 mr-2" />
                Новый счет
              </Button>
            </Link>
          </div>

          {/* Фильтры и поиск */}
          <Card className="mb-8 border-0 shadow-lg bg-white/80 backdrop-blur-sm">
            <CardContent className="pt-6">
              <div className="flex flex-col lg:flex-row gap-6">
                {/* Поиск */}
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                    <Input
                      placeholder="Поиск по названию счета..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-12 py-4 border-0 bg-gray-50/50 focus:bg-white focus:ring-2 focus:ring-blue-500 focus:ring-offset-0 rounded-xl text-base shadow-sm transition-all duration-200 hover:bg-gray-100/50"
                    />
                  </div>
                </div>

                {/* Фильтр по статусу */}
                <div className="flex gap-3">
                  <select
                    value={filterStatus}
                    onChange={(e) => setFilterStatus(e.target.value as FilterStatus)}
                    className="px-4 py-3 border-0 bg-gray-50/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:bg-white"
                  >
                    <option value="all">Все счета</option>
                    <option value="unpaid">Не оплачено</option>
                    <option value="paid">Оплачено</option>
                    <option value="pending">Ожидают оплаты</option>
                  </select>
                </div>

                {/* Сортировка */}
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleSort("createdAt")}
                    className="flex items-center gap-2 border-0 bg-gray-50/50 hover:bg-gray-100/50 px-4 py-3"
                  >
                    <Calendar className="h-4 w-4" />
                    Дата
                    <ArrowUpDown className="h-3 w-3" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleSort("total")}
                    className="flex items-center gap-2 border-0 bg-gray-50/50 hover:bg-gray-100/50 px-4 py-3"
                  >
                    <DollarSign className="h-4 w-4" />
                    Сумма
                    <ArrowUpDown className="h-3 w-3" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleSort("title")}
                    className="flex items-center gap-2 border-0 bg-gray-50/50 hover:bg-gray-100/50 px-4 py-3"
                  >
                    Название
                    <ArrowUpDown className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Статистика */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <Card className="bg-gradient-to-br from-blue-500 to-blue-600 text-white border-0 shadow-xl">
              <CardContent className="pt-6">
                <div className="flex items-center">
                  <Receipt className="h-10 w-10 opacity-90" />
                  <div className="ml-4">
                    <p className="text-sm font-medium opacity-90">Всего счетов</p>
                    <p className="text-3xl font-bold">{bills.length}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
              <CardContent className="pt-6">
                <div className="flex items-center">
                  <CheckCircle className="h-10 w-10 text-green-500" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Завершено</p>
                    <p className="text-3xl font-bold text-gray-900">
                      {bills.filter(bill => bill.participants.every(p => p.status === "paid")).length}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
              <CardContent className="pt-6">
                <div className="flex items-center">
                  <Clock className="h-10 w-10 text-orange-500" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">В ожидании</p>
                    <p className="text-3xl font-bold text-gray-900">
                      {bills.filter(bill => bill.participants.some(p => p.status === "unpaid")).length}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
              <CardContent className="pt-6">
                <div className="flex items-center">
                  <DollarSign className="h-10 w-10 text-purple-500" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Общая сумма</p>
                    <p className="text-3xl font-bold text-gray-900">
                      {formatCurrency(bills.reduce((sum, bill) => sum + bill.total, 0))}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Список счетов */}
          <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center text-xl">
                <Receipt className="h-6 w-6 mr-3 text-blue-600" />
                Счета ({filteredBills.length})
              </CardTitle>
              <CardDescription className="text-gray-600">
                {searchQuery && `Результаты поиска для "${searchQuery}"`}
                {filterStatus !== "all" && ` • Фильтр: ${filterStatus}`}
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-2">
              {filteredBills.length === 0 ? (
                <div className="text-center py-16">
                  <Receipt className="h-20 w-20 text-gray-300 mx-auto mb-6" />
                  <h3 className="text-2xl font-medium text-gray-900 mb-3">
                    {bills.length === 0 ? "Пока нет счетов" : "Счета не найдены"}
                  </h3>
                  <p className="text-gray-600 mb-6 max-w-md mx-auto">
                    {bills.length === 0
                      ? "Создайте свой первый счет, чтобы начать разделять расходы с друзьями"
                      : "Попробуйте изменить параметры поиска или фильтры"
                    }
                  </p>
                  {bills.length === 0 && (
                    <Link href="/bills/new">
                      <Button className="bg-blue-600 hover:bg-blue-700 border-0 shadow-lg px-8 py-3">
                        <Plus className="h-5 w-5 mr-2" />
                        Создать счет
                      </Button>
                    </Link>
                  )}
                </div>
              ) : (
                <div className="space-y-4">
                  {filteredBills.map((bill) => {
                    const status = getBillStatus(bill)
                    const userParticipant = bill.participants.find(p => p.user._id === session?.user?.id)
                    const totalPaid = bill.participants.filter(p => p.status === "paid").length
                    const totalParticipants = bill.participants.length

                    return (
                      <div
                        key={bill._id}
                        className="rounded-xl p-6 bg-gray-50/50 hover:bg-gray-100/50 transition-colors border-0 shadow-sm"
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-3 mb-3">
                              <h3 className="text-xl font-semibold text-gray-900">
                                {bill.title}
                              </h3>
                              {getStatusBadge(status)}
                            </div>

                            <div className="flex items-center flex-wrap gap-6 text-sm text-gray-600 mb-4">
                              <div className="flex items-center gap-2">
                                <Calendar className="h-4 w-4" />
                                {formatDate(new Date(bill.createdAt))}
                              </div>
                              <div className="flex items-center gap-2">
                                <DollarSign className="h-4 w-4" />
                                {formatCurrency(bill.total)}
                              </div>
                              <div className="flex items-center gap-2">
                                <Users className="h-4 w-4" />
                                {bill.participants.length} участник{bill.participants.length > 1 ? 'ов' : ''}
                              </div>
                              <div className="flex items-center gap-2">
                                <User className="h-4 w-4" />
                                Создатель: {bill.creator.name}
                              </div>
                            </div>

                            {/* Прогресс оплаты */}
                            <div className="flex items-center gap-3 mb-3">
                              <div className="flex-1 bg-gray-200 rounded-full h-3">
                                <div
                                  className="bg-green-500 h-3 rounded-full transition-all duration-500"
                                  style={{ width: `${(totalPaid / totalParticipants) * 100}%` }}
                                ></div>
                              </div>
                              <span className="text-sm text-gray-600 font-medium">
                                {totalPaid}/{totalParticipants} оплачено
                              </span>
                            </div>

                            {/* Информация о долге пользователя */}
                            {userParticipant && (
                              <div className="text-sm">
                                <span className="text-gray-600">Ваш долг: </span>
                                <span className={`font-semibold ${
                                  userParticipant.status === "paid"
                                    ? "text-green-600"
                                    : "text-red-600"
                                }`}>
                                  {formatCurrency(userParticipant.amountOwed)}
                                  {userParticipant.status === "paid" && " (оплачено)"}
                                </span>
                              </div>
                            )}
                          </div>

                          <div className="flex items-center gap-2">
                            <Link href={`/bills/${bill._id}`}>
                              <Button variant="outline" size="sm" className="border-0 bg-blue-50 hover:bg-blue-100 text-blue-600">
                                <Eye className="h-4 w-4 mr-2" />
                                Просмотр
                              </Button>
                            </Link>
                          </div>
                        </div>
                      </div>
                    )
                  })}
                </div>
              )}
            </CardContent>
          </Card>
      </div>
    </div>
  )
}
