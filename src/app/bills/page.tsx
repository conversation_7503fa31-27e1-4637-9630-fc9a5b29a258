"use client"

import { useSession } from "next-auth/react"
import Link from "next/link"
import { Navbar } from "@/components/layout/navbar"
import { Button } from "@/components/ui/button"
import { LoadingSpinner } from "@/components/ui/loading-spinner"
import { BillsFilters } from "@/components/bills/bills-filters"
import { BillsStats } from "@/components/bills/bills-stats"
import { BillsList } from "@/components/bills/bills-list"
import { useBills } from "@/hooks/use-api"
import { useBillsFilters } from "@/hooks/use-bills-filters"
import { Plus } from "lucide-react"

export default function BillsPage() {
  const { data: session } = useSession()
  const { data: bills = [], isLoading } = useBills()

  const {
    searchQuery,
    setSearchQuery,
    sortField,
    sortOrder,
    filterStatus,
    setFilterStatus,
    handleSort,
    filteredBills
  } = useBillsFilters({
    bills,
    userId: session?.user?.id
  })



  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-100 via-blue-50 to-indigo-100">
        <Navbar />
        <div className="container mx-auto px-4 py-8">
          <LoadingSpinner
            message="Загрузка счетов..."
            submessage="Получаем ваши данные"
          />
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-100 via-blue-50 to-indigo-100">
      <Navbar />

      <div className="container mx-auto px-4 py-8">
          {/* Header */}
          <div className="flex flex-col lg:flex-row lg:items-center justify-between mb-10 gap-4">
            <div>
              <h1 className="text-4xl font-bold text-gray-900 mb-2">Все счета</h1>
              <p className="text-lg text-gray-600">
                Управляйте всеми своими счетами в одном месте
              </p>
            </div>
            <Link href="/bills/new">
              <Button className="bg-blue-600 hover:bg-blue-700 border-0 shadow-lg px-8 py-3 text-base">
                <Plus className="h-5 w-5 mr-2" />
                Новый счет
              </Button>
            </Link>
          </div>

          {/* Фильтры и поиск */}
          <BillsFilters
            searchQuery={searchQuery}
            setSearchQuery={setSearchQuery}
            filterStatus={filterStatus}
            setFilterStatus={setFilterStatus}
            sortField={sortField}
            sortOrder={sortOrder}
            onSort={handleSort}
          />

          {/* Статистика */}
          <BillsStats bills={bills} />

          {/* Список счетов */}
          <BillsList
            bills={filteredBills}
            searchQuery={searchQuery}
            filterStatus={filterStatus}
          />
      </div>
    </div>
  )
}
