"use client"

import { useSession } from "next-auth/react"
import Link from "next/link"
import { Navbar } from "@/components/layout/navbar"
import { Button } from "@/components/ui/button"
import { LoadingSpinner } from "@/components/ui/loading-spinner"
import { StatsCards } from "@/components/dashboard/stats-cards"
import { RecentBills } from "@/components/dashboard/recent-bills"
import { QuickActions } from "@/components/dashboard/quick-actions"
import { useDashboardStats, useBills } from "@/hooks/use-api"
import { Plus } from "lucide-react"

export default function DashboardPage() {
  const { data: session } = useSession()
  const { data: stats, isLoading: statsLoading } = useDashboardStats()
  const { data: bills, isLoading: billsLoading } = useBills()

  const isLoading = statsLoading || billsLoading

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50/30 to-purple-50/30">
        <Navbar />
        <div className="container mx-auto px-4 py-8">
          <LoadingSpinner
            message="Загрузка дашборда..."
            submessage="Подготавливаем ваши данные"
          />
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-100 via-blue-50 to-indigo-100">
      <Navbar />

      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex flex-col lg:flex-row lg:items-center justify-between mb-10 gap-4">
          <div>
            <h1 className="text-4xl font-bold text-gray-900 mb-2">
              Добро пожаловать, {session?.user?.name}!
            </h1>
            <p className="text-lg text-gray-600">
              Управляйте своими счетами и следите за расходами
            </p>
          </div>
          <Link href="/bills/new">
            <Button className="bg-blue-600 hover:bg-blue-700 border-0 shadow-lg px-8 py-3 text-base">
              <Plus className="h-5 w-5 mr-2" />
              Новый счет
            </Button>
          </Link>
        </div>

        {/* Stats Cards */}
        {stats && <StatsCards stats={stats} />}

        {/* Recent Bills */}
        {bills && <RecentBills bills={bills} />}

        {/* Quick Actions */}
        <QuickActions />
      </div>
    </div>
  )
}
