// Единый источник истины для всех констант приложения

// ===== СТАТУСЫ =====

export const FRIEND_REQUEST_STATUSES = {
  PENDING: "pending",
  ACCEPTED: "accepted",
  REJECTED: "rejected"
} as const

export const PAYMENT_STATUSES = {
  PAID: "paid",
  UNPAID: "unpaid"
} as const

export const BILL_STATUSES = {
  COMPLETED: "completed",
  CREATOR: "creator",
  PAID: "paid",
  UNPAID: "unpaid"
} as const

// ===== СОРТИРОВКА И ФИЛЬТРАЦИЯ =====

export const SORT_FIELDS = {
  CREATED_AT: "createdAt",
  TOTAL: "total",
  TITLE: "title"
} as const

export const SORT_ORDERS = {
  ASC: "asc",
  DESC: "desc"
} as const

export const FILTER_STATUSES = {
  ALL: "all",
  PAID: "paid",
  UNPAID: "unpaid",
  PENDING: "pending"
} as const

// ===== АНАЛИТИКА =====

export const ANALYTICS_PERIODS = {
  WEEK: "week",
  MONTH: "month",
  QUARTER: "quarter",
  YEAR: "year"
} as const

// ===== НАВИГАЦИЯ =====

export const ROUTES = {
  HOME: "/",
  DASHBOARD: "/dashboard",
  BILLS: "/bills",
  BILLS_NEW: "/bills/new",
  BILLS_DETAIL: (id: string) => `/bills/${id}`,
  FRIENDS: "/friends",
  ANALYTICS: "/analytics",
  AUTH_LOGIN: "/auth/login",
  AUTH_REGISTER: "/auth/register"
} as const

export const API_ROUTES = {
  AUTH_REGISTER: "/api/auth/register",
  AUTH_LOGIN: "/api/auth/login",
  USER_ME: "/api/user/me",
  USERS_SEARCH: "/api/users/search",
  FRIENDS: "/api/friends",
  FRIENDS_REQUEST: "/api/friends/request",
  FRIENDS_REQUEST_RESPOND: (id: string) => `/api/friends/request/${id}`,
  BILLS: "/api/bills",
  BILLS_DETAIL: (id: string) => `/api/bills/${id}`,
  DASHBOARD_STATS: "/api/dashboard/stats",
  ANALYTICS: "/api/analytics"
} as const

// ===== ВАЛИДАЦИЯ =====

export const VALIDATION_LIMITS = {
  NAME_MIN: 2,
  NAME_MAX: 50,
  PASSWORD_MIN: 6,
  TITLE_MAX: 100,
  DESCRIPTION_MAX: 500,
  SEARCH_QUERY_MAX: 100,
  PAGINATION_MAX: 100
} as const

// ===== UI КОНСТАНТЫ =====

export const TOAST_DURATION = {
  SHORT: 3000,
  MEDIUM: 5000,
  LONG: 8000
} as const

export const LOADING_MESSAGES = {
  DASHBOARD: "Загрузка дашборда...",
  BILLS: "Загрузка счетов...",
  FRIENDS: "Загрузка друзей...",
  ANALYTICS: "Загрузка аналитики...",
  CREATING_BILL: "Создание счета...",
  SAVING: "Сохранение...",
  LOADING: "Загрузка..."
} as const

export const ERROR_MESSAGES = {
  NETWORK_ERROR: "Ошибка сети. Проверьте подключение к интернету.",
  UNAUTHORIZED: "Необходимо войти в систему",
  FORBIDDEN: "Недостаточно прав доступа",
  NOT_FOUND: "Ресурс не найден",
  SERVER_ERROR: "Внутренняя ошибка сервера",
  VALIDATION_ERROR: "Ошибка валидации данных",
  UNKNOWN_ERROR: "Произошла неизвестная ошибка"
} as const

export const SUCCESS_MESSAGES = {
  BILL_CREATED: "Счет успешно создан",
  BILL_UPDATED: "Счет обновлен",
  BILL_DELETED: "Счет удален",
  FRIEND_REQUEST_SENT: "Запрос на добавление в друзья отправлен",
  FRIEND_REQUEST_ACCEPTED: "Запрос принят",
  FRIEND_REQUEST_REJECTED: "Запрос отклонен",
  USER_REGISTERED: "Пользователь успешно зарегистрирован",
  LOGIN_SUCCESS: "Вход выполнен успешно"
} as const

// ===== QUERY KEYS =====

export const QUERY_KEYS = {
  CURRENT_USER: ["user", "current"] as const,
  FRIENDS: ["friends"] as const,
  BILLS: ["bills"] as const,
  BILL: (id: string) => ["bills", id] as const,
  DASHBOARD_STATS: ["dashboard", "stats"] as const,
  ANALYTICS: (period: string) => ["analytics", period] as const,
  SEARCH_USERS: (query: string) => ["users", "search", query] as const
} as const

// ===== CACHE TIMES (в миллисекундах) =====

export const CACHE_TIMES = {
  VERY_SHORT: 30 * 1000,      // 30 секунд
  SHORT: 1 * 60 * 1000,       // 1 минута
  MEDIUM: 5 * 60 * 1000,      // 5 минут
  LONG: 15 * 60 * 1000,       // 15 минут
  VERY_LONG: 60 * 60 * 1000   // 1 час
} as const

// ===== МАССИВЫ ДЛЯ ВАЛИДАЦИИ =====

export const FRIEND_REQUEST_STATUSES_ARRAY = Object.values(FRIEND_REQUEST_STATUSES)
export const PAYMENT_STATUSES_ARRAY = Object.values(PAYMENT_STATUSES)
export const SORT_FIELDS_ARRAY = Object.values(SORT_FIELDS)
export const SORT_ORDERS_ARRAY = Object.values(SORT_ORDERS)
export const FILTER_STATUSES_ARRAY = Object.values(FILTER_STATUSES)
export const ANALYTICS_PERIODS_ARRAY = Object.values(ANALYTICS_PERIODS)

// ===== ТИПЫ ИЗ КОНСТАНТ =====

export type FriendRequestStatus = typeof FRIEND_REQUEST_STATUSES[keyof typeof FRIEND_REQUEST_STATUSES]
export type PaymentStatus = typeof PAYMENT_STATUSES[keyof typeof PAYMENT_STATUSES]
export type SortField = typeof SORT_FIELDS[keyof typeof SORT_FIELDS]
export type SortOrder = typeof SORT_ORDERS[keyof typeof SORT_ORDERS]
export type FilterStatus = typeof FILTER_STATUSES[keyof typeof FILTER_STATUSES]
export type AnalyticsPeriod = typeof ANALYTICS_PERIODS[keyof typeof ANALYTICS_PERIODS]
