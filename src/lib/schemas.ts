// Единый источник истины для всех Zod схем валидации
import { z } from "zod"
import {
  FRIEND_REQUEST_STATUSES_ARRAY,
  PAYMENT_STATUSES_ARRAY,
  SORT_FIELDS_ARRAY,
  SORT_ORDERS_ARRAY,
  FILTER_STATUSES_ARRAY,
  ANALYTICS_PERIODS_ARRAY,
  VALIDATION_LIMITS
} from "./constants"

// ===== БАЗОВЫЕ СХЕМЫ =====

export const userSchema = z.object({
  _id: z.string(),
  name: z.string().min(VALIDATION_LIMITS.NAME_MIN, "Имя должно содержать минимум 2 символа"),
  email: z.string().email("Некорректный email"),
  image: z.string().optional(),
  bio: z.string().optional(),
})

export const friendSchema = z.object({
  _id: z.string(),
  name: z.string(),
  email: z.string().email(),
})

export const friendRequestSchema = z.object({
  _id: z.string(),
  from: z.object({
    _id: z.string(),
    name: z.string(),
    email: z.string().email(),
  }),
  to: z.object({
    _id: z.string(),
    name: z.string(),
    email: z.string().email(),
  }),
  status: z.enum(FRIEND_REQUEST_STATUSES_ARRAY as [string, ...string[]]),
  createdAt: z.string(),
  updatedAt: z.string(),
})

export const billParticipantSchema = z.object({
  user: z.string(),
  amount: z.number().positive("Сумма должна быть положительной"),
  paid: z.boolean(),
})

export const billItemConsumerSchema = z.object({
  user: z.string(),
  portions: z.number().positive("Количество порций должно быть положительным").default(1),
})

export const billItemSchema = z.object({
  name: z.string().min(1, "Название товара обязательно"),
  price: z.number().positive("Цена должна быть положительной"),
  consumers: z.array(billItemConsumerSchema).min(1, "Должен быть хотя бы один потребитель"),
})

export const billSchema = z.object({
  _id: z.string(),
  title: z.string().min(1, "Название счета обязательно"),
  description: z.string().optional(),
  totalAmount: z.number().positive("Общая сумма должна быть положительной"),
  createdBy: z.string(),
  participants: z.array(billParticipantSchema).min(1, "Должен быть хотя бы один участник"),
  items: z.array(billItemSchema).optional(),
  createdAt: z.string(),
  updatedAt: z.string(),
})

// ===== СХЕМЫ ДЛЯ API =====

export const createBillSchema = z.object({
  title: z.string().min(1, "Название счета обязательно").max(100, "Название слишком длинное"),
  description: z.string().max(500, "Описание слишком длинное").optional(),
  items: z.array(billItemSchema).min(1, "Должен быть хотя бы один товар"),
  selectedFriends: z.array(z.string()).min(1, "Должен быть выбран хотя бы один друг"),
})

export const updateBillSchema = createBillSchema.partial()

export const loginSchema = z.object({
  email: z.string().email("Некорректный email"),
  password: z.string().min(1, "Пароль обязателен"),
})

// Схема для API регистрации (без confirmPassword)
export const registerApiSchema = z.object({
  name: z.string().min(2, "Имя должно содержать минимум 2 символа").max(50, "Имя слишком длинное"),
  email: z.string().email("Некорректный email"),
  password: z.string().min(6, "Пароль должен содержать минимум 6 символов"),
})

// Схема для формы регистрации (с confirmPassword)
export const registerSchema = registerApiSchema.extend({
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Пароли не совпадают",
  path: ["confirmPassword"],
})

export const searchUsersSchema = z.object({
  query: z.string().min(1, "Поисковый запрос не может быть пустым").max(100, "Запрос слишком длинный"),
})

export const sendFriendRequestSchema = z.object({
  userId: z.string().min(1, "ID пользователя обязателен"),
})

export const respondToFriendRequestSchema = z.object({
  action: z.enum(["accept", "reject"]),
})

// ===== СХЕМЫ ДЛЯ ФИЛЬТРАЦИИ И СОРТИРОВКИ =====

export const sortFieldSchema = z.enum(SORT_FIELDS)
export const sortOrderSchema = z.enum(SORT_ORDERS)
export const filterStatusSchema = z.enum(FILTER_STATUSES)

export const billsFiltersSchema = z.object({
  searchQuery: z.string().default(""),
  sortField: sortFieldSchema.default("createdAt"),
  sortOrder: sortOrderSchema.default("desc"),
  filterStatus: filterStatusSchema.default("all"),
})

// ===== СХЕМЫ ДЛЯ АНАЛИТИКИ =====

export const analyticsPeriodSchema = z.enum(ANALYTICS_PERIODS)

export const analyticsFiltersSchema = z.object({
  period: analyticsPeriodSchema.default("month"),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
})

// ===== СХЕМЫ ДЛЯ QUERY ПАРАМЕТРОВ =====

export const paginationSchema = z.object({
  page: z.coerce.number().positive().default(1),
  limit: z.coerce.number().positive().max(100).default(10),
})

export const billsQuerySchema = billsFiltersSchema.merge(paginationSchema)

export const analyticsQuerySchema = z.object({
  period: analyticsPeriodSchema.default("month"),
})

// ===== СХЕМЫ ДЛЯ ОТВЕТОВ API =====

export const apiResponseSchema = <T extends z.ZodTypeAny>(dataSchema: T) =>
  z.object({
    data: dataSchema.optional(),
    error: z.string().optional(),
    message: z.string().optional(),
  })

export const paginatedResponseSchema = <T extends z.ZodTypeAny>(itemSchema: T) =>
  z.object({
    data: z.array(itemSchema),
    total: z.number(),
    page: z.number(),
    limit: z.number(),
    hasMore: z.boolean(),
  })

// ===== СХЕМЫ ДЛЯ СТАТИСТИКИ =====

export const dashboardStatsSchema = z.object({
  totalBills: z.number(),
  totalAmount: z.number(),
  totalOwed: z.number(),
  totalOwing: z.number(),
})

export const analyticsSchema = z.object({
  spendingTrends: z.array(z.object({
    date: z.string(),
    amount: z.number(),
  })),
  categoryBreakdown: z.array(z.object({
    category: z.string(),
    amount: z.number(),
  })),
  topFriends: z.array(z.object({
    name: z.string(),
    amount: z.number(),
  })),
  insights: z.object({
    averagePerBill: z.number(),
    mostActiveMonth: z.string(),
    totalSaved: z.number(),
  }),
})

// ===== ЭКСПОРТ ТИПОВ ИЗ СХЕМ =====

export type User = z.infer<typeof userSchema>
export type Friend = z.infer<typeof friendSchema>
export type FriendRequest = z.infer<typeof friendRequestSchema>
export type Bill = z.infer<typeof billSchema>
export type BillParticipant = z.infer<typeof billParticipantSchema>
export type BillItem = z.infer<typeof billItemSchema>
export type CreateBillForm = z.infer<typeof createBillSchema>
export type LoginForm = z.infer<typeof loginSchema>
export type RegisterForm = z.infer<typeof registerSchema>
export type BillsFilters = z.infer<typeof billsFiltersSchema>
export type AnalyticsFilters = z.infer<typeof analyticsFiltersSchema>
export type DashboardStats = z.infer<typeof dashboardStatsSchema>
export type Analytics = z.infer<typeof analyticsSchema>
