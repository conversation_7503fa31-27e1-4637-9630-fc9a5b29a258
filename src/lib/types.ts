// Единый источник истины для всех типов приложения
import type {
  FriendRequestStatus,
  SortField,
  SortOrder,
  FilterStatus,
  AnalyticsPeriod
} from "./constants"

// ===== БАЗОВЫЕ ТИПЫ =====

export interface User {
  _id: string
  name: string
  email: string
  image?: string
  bio?: string
}

export interface Friend {
  _id: string
  name: string
  email: string
}

export interface FriendRequest {
  _id: string
  from: { _id: string; name: string; email: string }
  to: { _id: string; name: string; email: string }
  status: FriendRequestStatus
  createdAt: string
  updatedAt: string
}

export interface BillParticipant {
  user: string
  amount: number
  paid: boolean
}

export interface BillItem {
  name: string
  price: number
  consumers: Array<{
    user: string
    portions: number
  }>
}

export interface Bill {
  _id: string
  title: string
  description?: string
  totalAmount: number
  createdBy: string
  participants: BillParticipant[]
  items?: BillItem[]
  createdAt: string
  updatedAt: string
}

export interface DashboardStats {
  totalBills: number
  totalAmount: number
  totalOwed: number
  totalOwing: number
}

export interface Analytics {
  spendingTrends: Array<{
    date: string
    amount: number
  }>
  categoryBreakdown: Array<{
    category: string
    amount: number
  }>
  topFriends: Array<{
    name: string
    amount: number
  }>
  insights: {
    averagePerBill: number
    mostActiveMonth: string
    totalSaved: number
  }
}

// ===== ФИЛЬТРАЦИЯ И СОРТИРОВКА =====
// Типы экспортируются из constants.ts

// ===== API ОТВЕТЫ =====

export interface ApiResponse<T> {
  data?: T
  error?: string
  message?: string
}

export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  limit: number
  hasMore: boolean
}

// ===== ФОРМЫ =====

export interface CreateBillForm {
  title: string
  description?: string
  items: Array<{
    name: string
    price: number
    consumers: Array<{
      user: string
      portions: number
    }>
  }>
  selectedFriends: string[]
}

export interface LoginForm {
  email: string
  password: string
}

export interface RegisterForm {
  name: string
  email: string
  password: string
  confirmPassword: string
}

export interface SearchUsersForm {
  query: string
}

// ===== СОСТОЯНИЯ UI =====

export interface LoadingState {
  isLoading: boolean
  error?: string
}

export interface FilterState {
  searchQuery: string
  sortField: SortField
  sortOrder: SortOrder
  filterStatus: FilterStatus
}

// ===== НАВИГАЦИЯ =====

export interface NavItem {
  href: string
  label: string
  icon?: React.ComponentType<{ className?: string }>
}

// ===== УВЕДОМЛЕНИЯ =====

export interface ToastMessage {
  title: string
  description?: string
  variant?: "default" | "destructive"
}

// ===== АНАЛИТИКА =====
// Типы экспортируются из constants.ts

export interface AnalyticsFilters {
  period: AnalyticsPeriod
  startDate?: string
  endDate?: string
}

// ===== УТИЛИТЫ =====

export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>

// ===== ЭКСПОРТ ТИПОВ ИЗ КОНСТАНТ =====
export type {
  FriendRequestStatus,
  PaymentStatus,
  SortField,
  SortOrder,
  FilterStatus,
  AnalyticsPeriod
} from "./constants"
