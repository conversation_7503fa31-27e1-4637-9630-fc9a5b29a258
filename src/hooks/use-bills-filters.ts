import { useState, useMemo } from "react"
import type { <PERSON>, SortField, SortOrder, FilterStatus } from "@/lib/types"

interface UseBillsFiltersProps {
  bills: Bill[]
  userId?: string
}

export function useBillsFilters({ bills, userId }: UseBillsFiltersProps) {
  const [searchQuery, setSearchQuery] = useState("")
  const [sortField, setSortField] = useState<SortField>("createdAt")
  const [sortOrder, setSortOrder] = useState<SortOrder>("desc")
  const [filterStatus, setFilterStatus] = useState<FilterStatus>("all")

  const filteredAndSortedBills = useMemo(() => {
    // Фильтрация
    const filtered = bills.filter(bill => {
      // Поиск по названию
      const matchesSearch = bill.title.toLowerCase().includes(searchQuery.toLowerCase())

      // Фильтрация по статусу
      let matchesStatus = true
      if (filterStatus !== "all" && userId) {
        const userParticipant = bill.participants.find(p => p.user === userId)
        const isCreator = bill.createdBy === userId

        if (filterStatus === "paid") {
          matchesStatus = userParticipant?.paid || isCreator
        } else if (filterStatus === "unpaid") {
          matchesStatus = userParticipant && !userParticipant.paid && !isCreator
        } else if (filterStatus === "pending") {
          matchesStatus = bill.participants.some(p => !p.paid)
        }
      }

      return matchesSearch && matchesStatus
    })

    // Сортировка
    filtered.sort((a, b) => {
      let aValue: number | string, bValue: number | string

      switch (sortField) {
        case "createdAt":
          aValue = new Date(a.createdAt).getTime()
          bValue = new Date(b.createdAt).getTime()
          break
        case "totalAmount":
          aValue = a.totalAmount
          bValue = b.totalAmount
          break
        case "title":
          aValue = a.title.toLowerCase()
          bValue = b.title.toLowerCase()
          break
        default:
          aValue = new Date(a.createdAt).getTime()
          bValue = new Date(b.createdAt).getTime()
      }

      if (sortOrder === "asc") {
        return aValue > bValue ? 1 : -1
      } else {
        return aValue < bValue ? 1 : -1
      }
    })

    return filtered
  }, [bills, searchQuery, sortField, sortOrder, filterStatus, userId])

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc")
    } else {
      setSortField(field)
      setSortOrder("desc")
    }
  }

  return {
    searchQuery,
    setSearchQuery,
    sortField,
    sortOrder,
    filterStatus,
    setFilterStatus,
    handleSort,
    filteredBills: filteredAndSortedBills
  }
}
