// Custom hooks for data fetching with React Query
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query"
import { api, type User, type Friend, type FriendRequest, type Bill, type DashboardStats, type Analytics } from "@/lib/api"
import { useToast } from "@/hooks/use-toast"

// Query keys for cache management
export const queryKeys = {
  currentUser: ["user", "current"] as const,
  friends: ["friends"] as const,
  bills: ["bills"] as const,
  bill: (id: string) => ["bills", id] as const,
  dashboardStats: ["dashboard", "stats"] as const,
  analytics: (period: string) => ["analytics", period] as const,
  searchUsers: (query: string) => ["users", "search", query] as const,
}

// User hooks
export function useCurrentUser() {
  return useQuery({
    queryKey: queryKeys.currentUser,
    queryFn: api.getCurrentUser,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

export function useSearchUsers(query: string, enabled = false) {
  return useQuery({
    queryKey: queryKeys.searchUsers(query),
    queryFn: () => api.searchUsers(query),
    enabled: enabled && query.trim().length > 0,
    staleTime: 30 * 1000, // 30 seconds
  })
}

// Friends hooks
export function useFriends() {
  return useQuery({
    queryKey: queryKeys.friends,
    queryFn: api.getFriends,
    staleTime: 2 * 60 * 1000, // 2 minutes
  })
}

export function useSendFriendRequest() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: api.sendFriendRequest,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.friends })
      toast({
        title: "Успешно",
        description: "Запрос на добавление в друзья отправлен",
      })
    },
    onError: () => {
      toast({
        title: "Ошибка",
        description: "Не удалось отправить запрос",
        variant: "destructive",
      })
    },
  })
}

export function useRespondToFriendRequest() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: ({ requestId, action }: { requestId: string; action: "accept" | "reject" }) =>
      api.respondToFriendRequest(requestId, action),
    onSuccess: (_, { action }) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.friends })
      toast({
        title: "Успешно",
        description: action === "accept" ? "Запрос принят" : "Запрос отклонен",
      })
    },
    onError: () => {
      toast({
        title: "Ошибка",
        description: "Не удалось обработать запрос",
        variant: "destructive",
      })
    },
  })
}

// Bills hooks
export function useBills() {
  return useQuery({
    queryKey: queryKeys.bills,
    queryFn: api.getBills,
    staleTime: 1 * 60 * 1000, // 1 minute
  })
}

export function useBill(id: string) {
  return useQuery({
    queryKey: queryKeys.bill(id),
    queryFn: () => api.getBill(id),
    enabled: !!id,
    staleTime: 2 * 60 * 1000, // 2 minutes
  })
}

export function useCreateBill() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: api.createBill,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.bills })
      queryClient.invalidateQueries({ queryKey: queryKeys.dashboardStats })
      toast({
        title: "Успешно",
        description: "Счет создан",
      })
    },
    onError: () => {
      toast({
        title: "Ошибка",
        description: "Не удалось создать счет",
        variant: "destructive",
      })
    },
  })
}

export function useUpdateBill() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<Bill> }) =>
      api.updateBill(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.bills })
      queryClient.invalidateQueries({ queryKey: queryKeys.bill(id) })
      queryClient.invalidateQueries({ queryKey: queryKeys.dashboardStats })
      toast({
        title: "Успешно",
        description: "Счет обновлен",
      })
    },
    onError: () => {
      toast({
        title: "Ошибка",
        description: "Не удалось обновить счет",
        variant: "destructive",
      })
    },
  })
}

export function useDeleteBill() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: api.deleteBill,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.bills })
      queryClient.invalidateQueries({ queryKey: queryKeys.dashboardStats })
      toast({
        title: "Успешно",
        description: "Счет удален",
      })
    },
    onError: () => {
      toast({
        title: "Ошибка",
        description: "Не удалось удалить счет",
        variant: "destructive",
      })
    },
  })
}

// Dashboard hooks
export function useDashboardStats() {
  return useQuery({
    queryKey: queryKeys.dashboardStats,
    queryFn: api.getDashboardStats,
    staleTime: 2 * 60 * 1000, // 2 minutes
  })
}

// Analytics hooks
export function useAnalytics(period: string) {
  return useQuery({
    queryKey: queryKeys.analytics(period),
    queryFn: () => api.getAnalytics(period),
    enabled: !!period,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}
