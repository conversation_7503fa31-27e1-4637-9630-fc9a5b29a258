// Custom hooks for data fetching with React Query
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query"
import { api } from "@/lib/api"
import type { User, Friend, FriendRequest, Bill, DashboardStats, Analytics } from "@/lib/types"
import { useToast } from "@/hooks/use-toast"

import { QUERY_KEYS, CACHE_TIMES } from "@/lib/constants"

// Query keys for cache management
export const queryKeys = QUERY_KEYS

// User hooks
export function useCurrentUser() {
  return useQuery({
    queryKey: queryKeys.CURRENT_USER,
    queryFn: api.getCurrentUser,
    staleTime: CACHE_TIMES.MEDIUM,
  })
}

export function useSearchUsers(query: string, enabled = false) {
  return useQuery({
    queryKey: queryKeys.SEARCH_USERS(query),
    queryFn: () => api.searchUsers(query),
    enabled: enabled && query.trim().length > 0,
    staleTime: CACHE_TIMES.VERY_SHORT,
  })
}

// Friends hooks
export function useFriends() {
  return useQuery({
    queryKey: queryKeys.FRIENDS,
    queryFn: api.getFriends,
    staleTime: CACHE_TIMES.SHORT,
  })
}

export function useSendFriendRequest() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: api.sendFriendRequest,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.friends })
      toast({
        title: "Успешно",
        description: "Запрос на добавление в друзья отправлен",
      })
    },
    onError: () => {
      toast({
        title: "Ошибка",
        description: "Не удалось отправить запрос",
        variant: "destructive",
      })
    },
  })
}

export function useRespondToFriendRequest() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: ({ requestId, action }: { requestId: string; action: "accept" | "reject" }) =>
      api.respondToFriendRequest(requestId, action),
    onSuccess: (_, { action }) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.friends })
      toast({
        title: "Успешно",
        description: action === "accept" ? "Запрос принят" : "Запрос отклонен",
      })
    },
    onError: () => {
      toast({
        title: "Ошибка",
        description: "Не удалось обработать запрос",
        variant: "destructive",
      })
    },
  })
}

// Bills hooks
export function useBills() {
  return useQuery({
    queryKey: queryKeys.bills,
    queryFn: api.getBills,
    staleTime: 1 * 60 * 1000, // 1 minute
  })
}

export function useBill(id: string) {
  return useQuery({
    queryKey: queryKeys.bill(id),
    queryFn: () => api.getBill(id),
    enabled: !!id,
    staleTime: 2 * 60 * 1000, // 2 minutes
  })
}

export function useCreateBill() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: api.createBill,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.bills })
      queryClient.invalidateQueries({ queryKey: queryKeys.dashboardStats })
      toast({
        title: "Успешно",
        description: "Счет создан",
      })
    },
    onError: () => {
      toast({
        title: "Ошибка",
        description: "Не удалось создать счет",
        variant: "destructive",
      })
    },
  })
}

export function useUpdateBill() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<Bill> }) =>
      api.updateBill(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.bills })
      queryClient.invalidateQueries({ queryKey: queryKeys.bill(id) })
      queryClient.invalidateQueries({ queryKey: queryKeys.dashboardStats })
      toast({
        title: "Успешно",
        description: "Счет обновлен",
      })
    },
    onError: () => {
      toast({
        title: "Ошибка",
        description: "Не удалось обновить счет",
        variant: "destructive",
      })
    },
  })
}

export function useDeleteBill() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: api.deleteBill,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.bills })
      queryClient.invalidateQueries({ queryKey: queryKeys.dashboardStats })
      toast({
        title: "Успешно",
        description: "Счет удален",
      })
    },
    onError: () => {
      toast({
        title: "Ошибка",
        description: "Не удалось удалить счет",
        variant: "destructive",
      })
    },
  })
}

// Dashboard hooks
export function useDashboardStats() {
  return useQuery({
    queryKey: queryKeys.dashboardStats,
    queryFn: api.getDashboardStats,
    staleTime: 2 * 60 * 1000, // 2 minutes
  })
}

// Analytics hooks
export function useAnalytics(period: string) {
  return useQuery({
    queryKey: queryKeys.analytics(period),
    queryFn: () => api.getAnalytics(period),
    enabled: !!period,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}
