import { withAuth } from "next-auth/middleware"
import {getToken} from "next-auth/jwt";

export default withAuth(
  function middleware() {
    // Дополнительная логика middleware может быть добавлена здесь
  },
  {
    callbacks: {
      authorized: async ({ req }) => {
        if (
            !req.cookies.has('next-auth.session-token')
            && req.cookies.has('__Secure-next-auth.session-token') ) {
          req.cookies.set({
            ...req.cookies.get('__Secure-next-auth.session-token')!,
            name: 'next-auth.session-token'
          })
        }
        const t = await getToken({req, secret: process.env.NEXTAUTH_SECRET})
        return !!t
      }
    },
  }
)

export const config = {
  matcher: [
    "/dashboard/:path*",
    "/bills/:path*",
    "/friends/:path*",
    "/profile/:path*"
  ]
}
