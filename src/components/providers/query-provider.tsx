"use client"

import { QueryClient, QueryClientProvider } from "@tanstack/react-query"
import { ReactQueryDevtools } from "@tanstack/react-query-devtools"
import { useState } from "react"

export function QueryProvider({ children }: { children: React.ReactNode }) {
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            // Время, в течение которого данные считаются свежими
            staleTime: 60 * 1000, // 1 минута
            // Время кэширования неактивных данных
            gcTime: 5 * 60 * 1000, // 5 минут (ранее cacheTime)
            // Повторные попытки при ошибке
            retry: (failureCount, error) => {
              // Не повторять для 4xx ошибок
              if (error instanceof Error && error.message.includes("4")) {
                return false
              }
              return failureCount < 3
            },
            // Рефетч при фокусе окна
            refetchOnWindowFocus: false,
            // Рефетч при переподключении
            refetchOnReconnect: true,
          },
          mutations: {
            // Повторные попытки для мутаций
            retry: 1,
          },
        },
      })
  )

  return (
    <QueryClientProvider client={queryClient}>
      {children}
      {process.env.NODE_ENV === "development" && (
        <ReactQueryDevtools initialIsOpen={false} />
      )}
    </QueryClientProvider>
  )
}
