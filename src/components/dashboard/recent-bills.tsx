import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { formatCurrency, formatDate } from "@/lib/utils"
import type { Bill } from "@/lib/types"
import { Plus, Receipt } from "lucide-react"

interface RecentBillsProps {
  bills: Bill[]
}

export function RecentBills({ bills }: RecentBillsProps) {
  return (
    <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm mb-8">
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center justify-between text-xl">
          <div className="flex items-center">
            <Receipt className="h-6 w-6 mr-3 text-blue-600" />
            Последние счета
          </div>
          <Link href="/bills">
            <Button variant="outline" size="sm" className="border-0 bg-blue-50 hover:bg-blue-100 text-blue-600">
              Все счета
            </Button>
          </Link>
        </CardTitle>
        <CardDescription className="text-gray-600">
          Ваши недавние счета и их статус
        </CardDescription>
      </CardHeader>
      <CardContent className="pt-2">
        {bills.length === 0 ? (
          <div className="text-center py-12">
            <Receipt className="h-16 w-16 text-gray-300 mx-auto mb-6" />
            <h3 className="text-xl font-medium text-gray-900 mb-3">
              Пока нет счетов
            </h3>
            <p className="text-gray-600 mb-6 max-w-md mx-auto">
              Создайте свой первый счет, чтобы начать разделять расходы с друзьями
            </p>
            <Link href="/bills/new">
              <Button className="bg-blue-600 hover:bg-blue-700 border-0 shadow-lg px-8 py-3">
                <Plus className="h-5 w-5 mr-2" />
                Создать счет
              </Button>
            </Link>
          </div>
        ) : (
          <div className="space-y-4">
            {bills.slice(0, 5).map((bill) => (
              <div
                key={bill._id}
                className="flex items-center justify-between p-4 rounded-xl bg-gray-50/50 hover:bg-gray-100/50 transition-colors border-0"
              >
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-900 mb-1">{bill.title}</h3>
                  <p className="text-sm text-gray-600 mb-3">
                    {formatDate(new Date(bill.createdAt))} • {formatCurrency(bill.totalAmount)}
                  </p>
                  <div className="flex items-center flex-wrap gap-2">
                    {bill.participants.slice(0, 3).map((participant, index) => (
                      <span
                        key={index}
                        className={`text-xs px-3 py-1 rounded-full font-medium ${
                          participant.paid
                            ? 'bg-green-100 text-green-700 border border-green-200'
                            : 'bg-orange-100 text-orange-700 border border-orange-200'
                        }`}
                      >
                        {formatCurrency(participant.amount)}
                      </span>
                    ))}
                    {bill.participants.length > 3 && (
                      <span className="text-xs px-3 py-1 rounded-full font-medium bg-gray-100 text-gray-700 border border-gray-200">
                        +{bill.participants.length - 3} еще
                      </span>
                    )}
                  </div>
                </div>
                <Link href={`/bills/${bill._id}`}>
                  <Button variant="outline" size="sm" className="border-0 bg-blue-50 hover:bg-blue-100 text-blue-600">
                    Подробнее
                  </Button>
                </Link>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
