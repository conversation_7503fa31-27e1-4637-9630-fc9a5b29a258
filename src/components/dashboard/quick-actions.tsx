import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Plus,
  Users,
  TrendingUp
} from "lucide-react"

export function QuickActions() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
      <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center text-lg">
            <Users className="h-6 w-6 mr-3 text-purple-600" />
            Быстрые действия
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4 pt-2">
          <Link href="/bills/new" className="block">
            <Button variant="outline" className="w-full justify-start border-0 bg-blue-50 hover:bg-blue-100 text-blue-700 py-3">
              <Plus className="h-5 w-5 mr-3" />
              Создать новый счет
            </Button>
          </Link>
          <Link href="/friends" className="block">
            <Button variant="outline" className="w-full justify-start border-0 bg-purple-50 hover:bg-purple-100 text-purple-700 py-3">
              <Users className="h-5 w-5 mr-3" />
              Управление друзьями
            </Button>
          </Link>
          <Link href="/analytics" className="block">
            <Button variant="outline" className="w-full justify-start border-0 bg-green-50 hover:bg-green-100 text-green-700 py-3">
              <TrendingUp className="h-5 w-5 mr-3" />
              Аналитика расходов
            </Button>
          </Link>
        </CardContent>
      </Card>

      <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center text-lg">
            <div className="w-6 h-6 bg-yellow-500 rounded-lg flex items-center justify-center text-white text-sm mr-3">
              💡
            </div>
            Полезные советы
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-2">
          <div className="space-y-4">
            <div className="p-4 bg-gradient-to-r from-blue-50 to-blue-100/50 rounded-xl border border-blue-100/50">
              <p className="text-sm text-blue-800 font-medium">💡 Добавляйте друзей для быстрого создания счетов</p>
            </div>
            <div className="p-4 bg-gradient-to-r from-green-50 to-green-100/50 rounded-xl border border-green-100/50">
              <p className="text-sm text-green-800 font-medium">📱 Отмечайте платежи сразу после получения</p>
            </div>
            <div className="p-4 bg-gradient-to-r from-purple-50 to-purple-100/50 rounded-xl border border-purple-100/50">
              <p className="text-sm text-purple-800 font-medium">🔔 Следите за уведомлениями о новых счетах</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
