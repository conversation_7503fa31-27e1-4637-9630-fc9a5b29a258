import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { formatCurrency } from "@/lib/utils"
import {
  TrendingUp,
  AlertCircle,
  Clock,
  CheckCircle
} from "lucide-react"
import type { DashboardStats } from "@/lib/api"

interface StatsCardsProps {
  stats: DashboardStats
}

export function StatsCards({ stats }: StatsCardsProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-10">
      <Card className="bg-gradient-to-br from-green-500 to-green-600 text-white border-0 shadow-xl">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
          <CardTitle className="text-sm font-medium opacity-90">Вам должны</CardTitle>
          <TrendingUp className="h-5 w-5 opacity-90" />
        </CardHeader>
        <CardContent>
          <div className="text-3xl font-bold mb-2">
            {formatCurrency(stats.totalOwed)}
          </div>
          <p className="text-xs opacity-90">К получению</p>
        </CardContent>
      </Card>

      <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
          <CardTitle className="text-sm font-medium text-gray-700">Вы должны</CardTitle>
          <AlertCircle className="h-5 w-5 text-red-500" />
        </CardHeader>
        <CardContent>
          <div className="text-3xl font-bold text-red-600 mb-2">
            {formatCurrency(stats.totalOwing)}
          </div>
          <p className="text-xs text-gray-600">К оплате</p>
        </CardContent>
      </Card>

      <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
          <CardTitle className="text-sm font-medium text-gray-700">Всего счетов</CardTitle>
          <Clock className="h-5 w-5 text-orange-500" />
        </CardHeader>
        <CardContent>
          <div className="text-3xl font-bold text-orange-600 mb-2">
            {stats.totalBills}
          </div>
          <p className="text-xs text-gray-600">Создано</p>
        </CardContent>
      </Card>

      <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
          <CardTitle className="text-sm font-medium text-gray-700">Общая сумма</CardTitle>
          <CheckCircle className="h-5 w-5 text-blue-500" />
        </CardHeader>
        <CardContent>
          <div className="text-3xl font-bold text-blue-600 mb-2">
            {formatCurrency(stats.totalAmount)}
          </div>
          <p className="text-xs text-gray-600">Всего потрачено</p>
        </CardContent>
      </Card>
    </div>
  )
}
