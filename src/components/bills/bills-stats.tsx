import { Card, CardContent } from "@/components/ui/card"
import { formatCurrency } from "@/lib/utils"
import {
  Receipt,
  CheckCircle,
  Clock,
  DollarSign
} from "lucide-react"
import type { Bill } from "@/lib/api"

interface BillsStatsProps {
  bills: Bill[]
}

export function BillsStats({ bills }: BillsStatsProps) {
  const completedBills = bills.filter(bill => 
    bill.participants.every(p => p.paid)
  ).length

  const pendingBills = bills.filter(bill => 
    bill.participants.some(p => !p.paid)
  ).length

  const totalAmount = bills.reduce((sum, bill) => sum + bill.totalAmount, 0)

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <Card className="bg-gradient-to-br from-blue-500 to-blue-600 text-white border-0 shadow-xl">
        <CardContent className="pt-6">
          <div className="flex items-center">
            <Receipt className="h-10 w-10 opacity-90" />
            <div className="ml-4">
              <p className="text-sm font-medium opacity-90">Всего счетов</p>
              <p className="text-3xl font-bold">{bills.length}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
        <CardContent className="pt-6">
          <div className="flex items-center">
            <CheckCircle className="h-10 w-10 text-green-500" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Завершено</p>
              <p className="text-3xl font-bold text-gray-900">{completedBills}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
        <CardContent className="pt-6">
          <div className="flex items-center">
            <Clock className="h-10 w-10 text-orange-500" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">В ожидании</p>
              <p className="text-3xl font-bold text-gray-900">{pendingBills}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
        <CardContent className="pt-6">
          <div className="flex items-center">
            <DollarSign className="h-10 w-10 text-purple-500" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Общая сумма</p>
              <p className="text-3xl font-bold text-gray-900">
                {formatCurrency(totalAmount)}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
