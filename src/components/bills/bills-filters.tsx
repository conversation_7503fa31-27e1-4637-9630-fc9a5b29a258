import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
import type { SortField, SortOrder, FilterStatus } from "@/lib/types"
import {
  Search,
  Calendar,
  DollarSign,
  ArrowUpDown
} from "lucide-react"

interface BillsFiltersProps {
  searchQuery: string
  setSearchQuery: (query: string) => void
  filterStatus: FilterStatus
  setFilterStatus: (status: FilterStatus) => void
  sortField: SortField
  sortOrder: SortOrder
  onSort: (field: SortField) => void
}

export function BillsFilters({
  searchQuery,
  setSearchQuery,
  filterStatus,
  setFilterStatus,
  onSort
}: BillsFiltersProps) {
  return (
    <Card className="mb-8 border-0 shadow-lg bg-white/80 backdrop-blur-sm">
      <CardContent className="pt-6">
        <div className="flex flex-col lg:flex-row gap-6">
          {/* Поиск */}
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <Input
                placeholder="Поиск по названию счета..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-12 py-4 border-0 bg-gray-50/50 focus:bg-white focus:ring-2 focus:ring-blue-500 focus:ring-offset-0 rounded-xl text-base shadow-sm transition-all duration-200 hover:bg-gray-100/50"
              />
            </div>
          </div>

          {/* Фильтр по статусу */}
          <div className="flex gap-3">
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value as FilterStatus)}
              className="px-4 py-3 border-0 bg-gray-50/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:bg-white"
            >
              <option value="all">Все счета</option>
              <option value="unpaid">Не оплачено</option>
              <option value="paid">Оплачено</option>
              <option value="pending">Ожидают оплаты</option>
            </select>
          </div>

          {/* Сортировка */}
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onSort("createdAt")}
              className="flex items-center gap-2 border-0 bg-gray-50/50 hover:bg-gray-100/50 px-4 py-3"
            >
              <Calendar className="h-4 w-4" />
              Дата
              <ArrowUpDown className="h-3 w-3" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onSort("totalAmount")}
              className="flex items-center gap-2 border-0 bg-gray-50/50 hover:bg-gray-100/50 px-4 py-3"
            >
              <DollarSign className="h-4 w-4" />
              Сумма
              <ArrowUpDown className="h-3 w-3" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onSort("title")}
              className="flex items-center gap-2 border-0 bg-gray-50/50 hover:bg-gray-100/50 px-4 py-3"
            >
              Название
              <ArrowUpDown className="h-3 w-3" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
