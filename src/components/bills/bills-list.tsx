import Link from "next/link"
import { useSession } from "next-auth/react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { formatCurrency, formatDate } from "@/lib/utils"
import {
  Receipt,
  Plus,
  Calendar,
  DollarSign,
  Users,
  User,
  Eye
} from "lucide-react"
import type { Bill } from "@/lib/api"

interface BillsListProps {
  bills: Bill[]
  searchQuery: string
  filterStatus: string
}

export function BillsList({ bills, searchQuery, filterStatus }: BillsListProps) {
  const { data: session } = useSession()

  const getBillStatus = (bill: Bill) => {
    const userParticipant = bill.participants.find(p => p.user === session?.user?.id)
    const isCreator = bill.createdBy === session?.user?.id
    const allPaid = bill.participants.every(p => p.paid)

    if (allPaid) return "completed"
    if (isCreator) return "creator"
    if (userParticipant?.paid) return "paid"
    return "unpaid"
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "completed":
        return <Badge className="bg-green-100 text-green-800">Завершен</Badge>
      case "creator":
        return <Badge className="bg-blue-100 text-blue-800">Создатель</Badge>
      case "paid":
        return <Badge className="bg-green-100 text-green-800">Оплачено</Badge>
      case "unpaid":
        return <Badge className="bg-red-100 text-red-800">Не оплачено</Badge>
      default:
        return <Badge className="bg-gray-100 text-gray-800">Неизвестно</Badge>
    }
  }

  return (
    <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center text-xl">
          <Receipt className="h-6 w-6 mr-3 text-blue-600" />
          Счета ({bills.length})
        </CardTitle>
        <CardDescription className="text-gray-600">
          {searchQuery && `Результаты поиска для "${searchQuery}"`}
          {filterStatus !== "all" && ` • Фильтр: ${filterStatus}`}
        </CardDescription>
      </CardHeader>
      <CardContent className="pt-2">
        {bills.length === 0 ? (
          <div className="text-center py-16">
            <Receipt className="h-20 w-20 text-gray-300 mx-auto mb-6" />
            <h3 className="text-2xl font-medium text-gray-900 mb-3">
              Счета не найдены
            </h3>
            <p className="text-gray-600 mb-6 max-w-md mx-auto">
              Попробуйте изменить параметры поиска или фильтры
            </p>
            <Link href="/bills/new">
              <Button className="bg-blue-600 hover:bg-blue-700 border-0 shadow-lg px-8 py-3">
                <Plus className="h-5 w-5 mr-2" />
                Создать счет
              </Button>
            </Link>
          </div>
        ) : (
          <div className="space-y-4">
            {bills.map((bill) => {
              const status = getBillStatus(bill)
              const userParticipant = bill.participants.find(p => p.user === session?.user?.id)
              const totalPaid = bill.participants.filter(p => p.paid).length
              const totalParticipants = bill.participants.length

              return (
                <div
                  key={bill._id}
                  className="rounded-xl p-6 bg-gray-50/50 hover:bg-gray-100/50 transition-colors border-0 shadow-sm"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-3">
                        <h3 className="text-xl font-semibold text-gray-900">
                          {bill.title}
                        </h3>
                        {getStatusBadge(status)}
                      </div>

                      <div className="flex items-center flex-wrap gap-6 text-sm text-gray-600 mb-4">
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4" />
                          {formatDate(new Date(bill.createdAt))}
                        </div>
                        <div className="flex items-center gap-2">
                          <DollarSign className="h-4 w-4" />
                          {formatCurrency(bill.totalAmount)}
                        </div>
                        <div className="flex items-center gap-2">
                          <Users className="h-4 w-4" />
                          {bill.participants.length} участник{bill.participants.length > 1 ? 'ов' : ''}
                        </div>
                      </div>

                      {/* Прогресс оплаты */}
                      <div className="flex items-center gap-3 mb-3">
                        <div className="flex-1 bg-gray-200 rounded-full h-3">
                          <div
                            className="bg-green-500 h-3 rounded-full transition-all duration-500"
                            style={{ width: `${(totalPaid / totalParticipants) * 100}%` }}
                          ></div>
                        </div>
                        <span className="text-sm text-gray-600 font-medium">
                          {totalPaid}/{totalParticipants} оплачено
                        </span>
                      </div>

                      {/* Информация о долге пользователя */}
                      {userParticipant && (
                        <div className="text-sm">
                          <span className="text-gray-600">Ваш долг: </span>
                          <span className={`font-semibold ${
                            userParticipant.paid
                              ? "text-green-600"
                              : "text-red-600"
                          }`}>
                            {formatCurrency(userParticipant.amount)}
                            {userParticipant.paid && " (оплачено)"}
                          </span>
                        </div>
                      )}
                    </div>

                    <div className="flex items-center gap-2">
                      <Link href={`/bills/${bill._id}`}>
                        <Button variant="outline" size="sm" className="border-0 bg-blue-50 hover:bg-blue-100 text-blue-600">
                          <Eye className="h-4 w-4 mr-2" />
                          Просмотр
                        </Button>
                      </Link>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
