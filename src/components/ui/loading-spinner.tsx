interface LoadingSpinnerProps {
  message?: string
  submessage?: string
}

export function LoadingSpinner({ 
  message = "Загрузка...", 
  submessage = "Подготавливаем ваши данные" 
}: LoadingSpinnerProps) {
  return (
    <div className="flex items-center justify-center h-96">
      <div className="text-center">
        <div className="relative">
          <div className="animate-spin rounded-full h-16 w-16 border-4 border-blue-200 border-t-blue-600 mx-auto"></div>
          <div 
            className="absolute inset-0 rounded-full h-16 w-16 border-4 border-transparent border-r-purple-400 animate-spin mx-auto" 
            style={{ animationDirection: 'reverse', animationDuration: '1.5s' }}
          ></div>
        </div>
        <p className="mt-6 text-lg text-gray-600 font-medium">{message}</p>
        <p className="mt-2 text-sm text-gray-500">{submessage}</p>
      </div>
    </div>
  )
}
